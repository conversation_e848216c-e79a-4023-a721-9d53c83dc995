/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.rest;

import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.project.domain.ProjectElements;
import com.jinghang.cash.modules.project.service.ProjectElementsService;
import com.jinghang.cash.modules.project.domain.dto.ProjectElementsQueryCriteria;
import com.jinghang.cash.utils.PageResult;
import lombok.RequiredArgsConstructor;
import java.util.List;

import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
* <AUTHOR>
* @date 2025-08-25
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "project")
@RequestMapping("/api/projectElements")
public class ProjectElementsController {

    private final ProjectElementsService projectElementsService;

    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('projectElements:list')")
    public void exportProjectElements(HttpServletResponse response, ProjectElementsQueryCriteria criteria) throws IOException {
       // projectElementsService.download(projectElementsService.queryAll(criteria), response);
    }

    @GetMapping("/listPage")
    @ApiOperation("查询project")
    @PreAuthorize("@el.check('projectElements:listPage')")
    public RestResult<PageResult<ProjectElements>> queryProjectElementsPage(ProjectElementsQueryCriteria criteria){
        Page<Object> page = new Page<>(criteria.getPage(), criteria.getSize());
        return RestResult.success(projectElementsService.queryAll(criteria, page));
    }

    @GetMapping("/list")
    @ApiOperation("查询project")
    @PreAuthorize("@el.check('projectElements:list')")
    public RestResult<List<ProjectElements>> queryProjectElements(ProjectElementsQueryCriteria criteria){
        return RestResult.success(projectElementsService.queryAll(criteria));
    }

    @GetMapping("/info")
    @PreAuthorize("@el.check('projectElements:info')")
    public RestResult<ProjectElements> queryProjectElementsInfo(@RequestParam("projectCode")String projectCode){
        return RestResult.success(projectElementsService.queryProjectElementsInfo(projectCode));
    }


    @PostMapping("/add")
    @PreAuthorize("@el.check('projectElements:add')")
    public RestResult<Object> createProjectElements(@Validated @RequestBody ProjectElements resources){
        projectElementsService.create(resources);
        return RestResult.success();
    }

    @PutMapping("/edit")
    @PreAuthorize("@el.check('projectElements:edit')")
    public RestResult<Object> updateProjectElements(@Validated @RequestBody ProjectElements resources){
        projectElementsService.update(resources);
        return RestResult.success();
    }

    @DeleteMapping("/del")
    @PreAuthorize("@el.check('projectElements:del')")
    public RestResult<Object> deleteProjectElements(@ApiParam(value = "传ID数组[]") @RequestBody List<String> ids) {
        projectElementsService.deleteAll(ids);
        return RestResult.success();
    }
}