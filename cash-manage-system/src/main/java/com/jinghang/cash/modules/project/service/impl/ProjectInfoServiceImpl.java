package com.jinghang.cash.modules.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghang.cash.api.dto.ProjectElementsExtDto;
import com.jinghang.cash.api.dto.ProjectElementsDto;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.api.enums.*;
import com.jinghang.cash.modules.project.mapper.ProjectElementsExtMapper;
import com.jinghang.cash.modules.project.mapper.ProjectElementsMapper;
import com.jinghang.cash.modules.project.mapper.ProjectInfoMapper;
import com.jinghang.cash.modules.project.domain.ProjectElements;
import com.jinghang.cash.modules.project.domain.ProjectElementsExt;
import com.jinghang.cash.modules.project.domain.ProjectInfo;
import com.jinghang.cash.modules.project.domain.dto.ProjectInfoQueryCriteria;
import com.jinghang.cash.modules.project.service.ProjectInfoService;
import com.jinghang.cash.modules.project.service.domain.vo.ProjectElementsExtVo;
import com.jinghang.cash.modules.project.service.domain.vo.ProjectElementsVo;
import com.jinghang.cash.modules.project.service.domain.vo.ProjectInfoVo;
import com.jinghang.cash.utils.PageResult;
import com.jinghang.cash.utils.PageUtil;
import com.jinghang.cash.utils.RedisUtils;
import com.jinghang.cash.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 项目信息服务实现类
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 10:16
 */
@Service
@RequiredArgsConstructor
public class ProjectInfoServiceImpl implements ProjectInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoServiceImpl.class);

    @Autowired
    private ProjectInfoMapper projectInfoMapper;

    @Autowired
    private ProjectElementsMapper projectElementsMapper;

    @Autowired
    private ProjectElementsExtMapper projectElementsExtMapper;


    private final RedisUtils redisUtils;

    /**
     * 项目信息缓存key前缀
     */
    private static final String PROJECT_INFO_CACHE_PREFIX = "PROJECT_INFO_";

    /**
     * 默认缓存过期时间：24 * 7小时
     */
    private static final Duration CACHE_DURATION = Duration.ofHours(24 * 7);

    /**
     * 根据项目编码查询项目完整信息（带缓存）
     *
     * @param projectCode 项目编码
     * @return 项目完整信息dto
     */
    @Override
    public ProjectInfoDto queryProjectInfo(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.info("项目编码为空，无法查询项目信息");
            return null;
        }

        // 构建缓存key
        String cacheKey = PROJECT_INFO_CACHE_PREFIX + projectCode;

        try {
            // 1. 先从缓存中获取
            Object cachedData = redisUtils.get(cacheKey);
            if (cachedData instanceof ProjectInfoDto) {
                return (ProjectInfoDto) cachedData;
            }

            // 2. 缓存中没有，从数据库查询
            ProjectInfoDto projectInfoDto = queryProjectInfoFromDatabase(projectCode);

            // 3. 查询结果放入缓存
            if (projectInfoDto != null) {
                Duration cacheDuration = calculateCacheDuration(projectInfoDto.getElements());
                redisUtils.set(cacheKey, projectInfoDto, cacheDuration.getSeconds(), TimeUnit.SECONDS);
            }

            return projectInfoDto;

        } catch (Exception e) {
            logger.info("查询项目完整信息异常，projectCode: {}", projectCode, e);
            throw new RuntimeException("查询项目信息失败", e);
        }
    }

    /**
     * 从数据库查询项目完整信息
     *
     * @param projectCode 项目编码
     * @return 项目完整信息dto
     */
    private ProjectInfoDto queryProjectInfoFromDatabase(String projectCode) {
        // 1. 查询项目基本信息，只查询状态为启用的项目
        ProjectInfo projectInfo = projectInfoMapper.selectByProjectCode(projectCode);
        if (projectInfo == null || !AbleStatus.ENABLE.equals(projectInfo.getEnabled())) {
            return null;
        }

        // 2. 使用统一的构建方法构建完整信息
        ProjectInfoDto projectInfoDto = buildProjectInfoDto(projectInfo);
        return projectInfoDto;
    }

    /**
     * 查询项目要素，优先查询临时配置，如果没有或已过期则查询长期配置
     *
     * @param projectCode 项目编码
     * @return 项目要素
     */
    private ProjectElements queryProjectElements(String projectCode) {
        LocalDateTime currentTime = LocalDateTime.now();

        // 1. 先查询有效的临时项目要素
        ProjectElements temporaryElements = projectElementsMapper.selectValidTemporaryElements(
                projectCode, AbleStatus.ENABLE, "TEMPORARY", currentTime);

        if (temporaryElements != null) {
            return temporaryElements;
        }

        // 2. 没有有效的临时配置，查询长期配置
        ProjectElements longtimeElements = projectElementsMapper.selectByProjectCodeAndEnabledAndProjectDurationType(
                projectCode, AbleStatus.ENABLE, "LONGTIME");

        return longtimeElements;
    }

    /**
     * 计算缓存时长，如果是临时配置则使用临时配置的结束时间，否则使用默认时长
     *
     * @param elementsDto 项目要素dto
     * @return 缓存时长
     */
    private Duration calculateCacheDuration(ProjectElementsDto elementsDto) {
        if (elementsDto == null) {
            return CACHE_DURATION;
        }

        // 如果是临时配置且有结束时间，计算到结束时间的时长
        if ("TEMPORARY".equals(elementsDto.getProjectDurationType())
                && elementsDto.getTempEndTime() != null) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endTime = elementsDto.getTempEndTime();

            if (endTime.isAfter(now)) {
                return Duration.between(now, endTime);
            }
        }

        // 长期配置或临时配置已过期，使用默认缓存时长
        return CACHE_DURATION;
    }

    /**
     * 查询所有生效项目信息
     *
     * @return 所有生效项目信息列表
     */
    @Override
    public List<ProjectInfoDto> queryAllEnabledProjects() {
        try {
            // 1. 查询所有启用状态的项目基本信息
            List<ProjectInfo> projectInfoList = projectInfoMapper.selectAllEnabledProjects();

            if (projectInfoList == null || projectInfoList.isEmpty()) {
                return new ArrayList<>();
            }

            // 2. 转换为DTO并填充完整信息
            List<ProjectInfoDto> projectInfoDtoList = new ArrayList<>();
            for (ProjectInfo projectInfo : projectInfoList) {
                ProjectInfoDto projectInfoDto = buildProjectInfoDto(projectInfo);
                if (projectInfoDto != null) {
                    projectInfoDtoList.add(projectInfoDto);
                }
            }

            return projectInfoDtoList;
        } catch (Exception e) {
            logger.error("查询所有生效项目信息异常", e);
            throw new RuntimeException("查询所有生效项目信息失败", e);
        }
    }

    /**
     * 构建项目完整信息DTO
     *
     * @param projectInfo 项目基本信息
     * @return 项目完整信息DTO
     */
    private ProjectInfoDto buildProjectInfoDto(ProjectInfo projectInfo) {
        try {
            // 1. 转换基本信息为DTO
            ProjectInfoDto projectInfoDto = new ProjectInfoDto();
            BeanUtils.copyProperties(projectInfo, projectInfoDto);
            // 手动处理枚举字段转换
            convertProjectInfoEnums(projectInfo, projectInfoDto);

            // 2. 查询项目要素
            ProjectElements elements = queryProjectElements(projectInfo.getProjectCode());
            if (ObjectUtil.isNotEmpty(elements)) {
                ProjectElementsDto elementsDto = new ProjectElementsDto();
                BeanUtil.copyProperties(elements, elementsDto);
                // 手动处理枚举字段转换
                convertProjectElementsEnums(elements, elementsDto);
                projectInfoDto.setElements(elementsDto);
            } else {
                projectInfoDto.setElements(null);
            }

            // 3. 查询项目要素扩展
            ProjectElementsExt elementsExt = null;
            if (elements != null) {
                elementsExt = projectElementsExtMapper.selectByParentId(elements.getId());
            }
            ProjectElementsExtDto elementsExtDto = new ProjectElementsExtDto();
            if (elementsExt != null) {
                BeanUtils.copyProperties(elementsExt, elementsExtDto);
                // 手动处理枚举字段转换
                convertProjectElementsExtEnums(elementsExt, elementsExtDto);
                projectInfoDto.setElementsExt(elementsExtDto);
            }

            return projectInfoDto;
        } catch (Exception e) {
            logger.info("构建项目完整信息异常，projectCode: {}", projectInfo.getProjectCode(), e);
            return null;
        }
    }

    /**
     * 转换ProjectInfo枚举字段
     */
    private void convertProjectInfoEnums(ProjectInfo source, ProjectInfoDto target) {
        if (source.getEnabled() != null) {
            target.setEnabled(source.getEnabled());
        }
        // 如果有其他枚举字段，在这里添加
    }

    /**
     * 转换ProjectElements枚举字段
     */
    private void convertProjectElementsEnums(ProjectElements source, ProjectElementsDto target) {
        if (source.getSupportedRepayTypes() != null) {
            target.setSupportedRepayTypes(source.getSupportedRepayTypes());
        }
        if (source.getCapitalRoute() != null) {
            target.setCapitalRoute(source.getCapitalRoute());
        }
        if (source.getProjectDurationType() != null) {
            target.setProjectDurationType(source.getProjectDurationType());
        }
        if (source.getEnabled() != null) {
            target.setEnabled(source.getEnabled());
        }
        if (source.getGraceNext() != null) {
            target.setGraceNext(source.getGraceNext());
        }
    }

    /**
     * 转换ProjectElementsExt枚举字段
     */
    private void convertProjectElementsExtEnums(ProjectElementsExt source, ProjectElementsExtDto target) {
        // 需要先检查source中的字符串字段，然后转换为枚举
        if (StringUtils.isNotBlank(source.getAllowCrossDayRepay())) {
            try {
                target.setAllowCrossDayRepay(ActiveInactive.valueOf(source.getAllowCrossDayRepay()));
            } catch (IllegalArgumentException e) {
                logger.warn("转换AllowCrossDayRepay枚举失败: {}", source.getAllowCrossDayRepay());
            }
        }
        if (StringUtils.isNotBlank(source.getRiskModelChannel())) {
            try {
                target.setRiskModelChannel(RiskModelChannel.valueOf(source.getRiskModelChannel()));
            } catch (IllegalArgumentException e) {
                logger.warn("转换RiskModelChannel枚举失败: {}", source.getRiskModelChannel());
            }
        }
        if (StringUtils.isNotBlank(source.getLoanPaymentChannel())) {
            try {
                target.setLoanPaymentChannel(LoanPayChannel.valueOf(source.getLoanPaymentChannel()));
            } catch (IllegalArgumentException e) {
                logger.warn("转换LoanPaymentChannel枚举失败: {}", source.getLoanPaymentChannel());
            }
        }
        if (StringUtils.isNotBlank(source.getDeductionBindCardChannel())) {
            try {
                target.setDeductionBindCardChannel(DeductChannel.valueOf(source.getDeductionBindCardChannel()));
            } catch (IllegalArgumentException e) {
                logger.warn("转换DeductionBindCardChannel枚举失败: {}", source.getDeductionBindCardChannel());
            }
        }
        if (StringUtils.isNotBlank(source.getSignChannel())) {
            try {
                target.setSignChannel(SignChannel.valueOf(source.getSignChannel()));
            } catch (IllegalArgumentException e) {
                logger.warn("转换SignChannel枚举失败: {}", source.getSignChannel());
            }
        }
        // 继续添加其他枚举字段的转换...
    }

    /**
     * 转换ProjectInfoDto枚举字段到Entity（反向转换）
     */
    private void convertProjectInfoDtoToEntity(ProjectInfoDto source, ProjectInfo target) {
        if (source.getEnabled() != null) {
            target.setEnabled(source.getEnabled());
        }
    }

    /**
     * 转换ProjectElementsDto枚举字段到Entity（反向转换）
     */
    private void convertProjectElementsDtoToEntity(ProjectElementsDto source, ProjectElements target) {
        if (source.getSupportedRepayTypes() != null) {
            target.setSupportedRepayTypes(source.getSupportedRepayTypes());
        }
        if (source.getCapitalRoute() != null) {
            target.setCapitalRoute(source.getCapitalRoute());
        }
        if (source.getProjectDurationType() != null) {
            target.setProjectDurationType(source.getProjectDurationType());
        }
        if (source.getEnabled() != null) {
            target.setEnabled(source.getEnabled());
        }
        if (source.getGraceNext() != null) {
            target.setGraceNext(source.getGraceNext());
        }
    }

    /**
     * 转换ProjectElementsExtDto枚举字段到Entity（反向转换）
     */
    private void convertProjectElementsExtDtoToEntity(ProjectElementsExtDto source, ProjectElementsExt target) {
        if (source.getAllowCrossDayRepay() != null) {
            target.setAllowCrossDayRepay(source.getAllowCrossDayRepay().name());
        }
        if (source.getRiskModelChannel() != null) {
            target.setRiskModelChannel(source.getRiskModelChannel().name());
        }
        if (source.getLoanPaymentChannel() != null) {
            target.setLoanPaymentChannel(source.getLoanPaymentChannel().name());
        }
        if (source.getDeductionBindCardChannel() != null) {
            target.setDeductionBindCardChannel(source.getDeductionBindCardChannel().name());
        }
        if (source.getSignChannel() != null) {
            target.setSignChannel(source.getSignChannel().name());
        }
        if (source.getSmsChannel() != null) {
            target.setSmsChannel(source.getSmsChannel().name());
        }
        if (source.getGracePeriodType() != null) {
            target.setGracePeriodType(source.getGracePeriodType().name());
        }
        // 继续添加其他枚举字段的转换...
    }

    /**
     * 转换ProjectInfo枚举字段到VO
     */
    private void convertProjectInfoToVo(ProjectInfo source, ProjectInfoVo target) {
        // ProjectInfoVo 中没有枚举字段，只需要基本拷贝
    }

    /**
     * 转换ProjectElements枚举字段到VO
     */
    private void convertProjectElementsToVo(ProjectElements source, ProjectElementsVo target) {
        if (source.getSupportedRepayTypes() != null) {
            target.setSupportedRepayTypes(source.getSupportedRepayTypes());
        }
        if (source.getCapitalRoute() != null) {
            target.setCapitalRoute(source.getCapitalRoute());
        }
        if (source.getProjectDurationType() != null) {
            target.setProjectDurationType(source.getProjectDurationType());
        }
        if (source.getEnabled() != null) {
            target.setEnabled(source.getEnabled());
        }
        if (source.getGraceNext() != null) {
            target.setGraceNext(source.getGraceNext());
        }
    }

    /**
     * 转换ProjectElementsExt枚举字段到VO
     */
    private void convertProjectElementsExtToVo(ProjectElementsExt source, ProjectElementsExtVo target) {
        // 从字符串转换为枚举
        if (StringUtils.isNotBlank(source.getAllowCrossDayRepay())) {
            try {
                target.setAllowCrossDayRepay(ActiveInactive.valueOf(source.getAllowCrossDayRepay()));
            } catch (IllegalArgumentException e) {
                logger.warn("转换AllowCrossDayRepay枚举失败: {}", source.getAllowCrossDayRepay());
            }
        }
        if (StringUtils.isNotBlank(source.getRiskModelChannel())) {
            try {
                target.setRiskModelChannel(RiskModelChannel.valueOf(source.getRiskModelChannel()));
            } catch (IllegalArgumentException e) {
                logger.warn("转换RiskModelChannel枚举失败: {}", source.getRiskModelChannel());
            }
        }
        if (StringUtils.isNotBlank(source.getLoanPaymentChannel())) {
            try {
                target.setLoanPaymentChannel(LoanPayChannel.valueOf(source.getLoanPaymentChannel()));
            } catch (IllegalArgumentException e) {
                logger.warn("转换LoanPaymentChannel枚举失败: {}", source.getLoanPaymentChannel());
            }
        }
        if (StringUtils.isNotBlank(source.getDeductionBindCardChannel())) {
            try {
                target.setDeductionBindCardChannel(DeductChannel.valueOf(source.getDeductionBindCardChannel()));
            } catch (IllegalArgumentException e) {
                logger.warn("转换DeductionBindCardChannel枚举失败: {}", source.getDeductionBindCardChannel());
            }
        }
        if (StringUtils.isNotBlank(source.getSignChannel())) {
            try {
                target.setSignChannel(SignChannel.valueOf(source.getSignChannel()));
            } catch (IllegalArgumentException e) {
                logger.warn("转换SignChannel枚举失败: {}", source.getSignChannel());
            }
        }
        if (StringUtils.isNotBlank(source.getSmsChannel())) {
            try {
                target.setSmsChannel(SmsChannel.valueOf(source.getSmsChannel()));
            } catch (IllegalArgumentException e) {
                logger.warn("转换SmsChannel枚举失败: {}", source.getSmsChannel());
            }
        }
        if (StringUtils.isNotBlank(source.getGracePeriodType())) {
            try {
                target.setGracePeriodType(GracePeriodType.valueOf(source.getGracePeriodType()));
            } catch (IllegalArgumentException e) {
                logger.warn("转换GracePeriodType枚举失败: {}", source.getGracePeriodType());
            }
        }
    }

    /**
     * 清除项目信息缓存
     *
     * @param projectCode 项目编码
     */
    @Override
    public void clearProjectInfoCache(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.info("项目编码为空，无法清除缓存");
            return;
        }

        String cacheKey = PROJECT_INFO_CACHE_PREFIX + projectCode;
        try {
            redisUtils.del(cacheKey);
        } catch (Exception e) {
            logger.info("清除项目信息缓存异常，projectCode: {}", projectCode, e);
        }
    }


    @Override
    public PageResult<ProjectInfoVo> queryAllPage(ProjectInfoQueryCriteria criteria){
        Page<ProjectInfoVo> page = new Page<>(criteria.getPage(),criteria.getSize());
        Page<ProjectInfoVo> pageList = projectInfoMapper.queryProjectInfoPage(page, criteria);
        return PageUtil.toPage(pageList.getRecords(),pageList.getTotal());
    }

    @Override
    public List<ProjectInfo> queryAll(ProjectInfoQueryCriteria criteria){
        //return projectInfoMapper.selectList(criteria);
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProjectInfoDto resources) {
        this.checkProjectCode(resources.getProjectCode());
        ProjectInfo info = new ProjectInfo();
        BeanUtil.copyProperties(resources,info);
        // 手动处理枚举字段转换
        convertProjectInfoDtoToEntity(resources, info);

        String id = "P" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
        info.setId(id);
        String userId = String.valueOf(SecurityUtils.getCurrentUserId());
        LocalDateTime dateTime = LocalDateTime.now();
        info.setCreatedBy(userId);
        info.setCreatedTime(dateTime);
        projectInfoMapper.insert(info);

        if(ObjectUtil.isNotEmpty(resources.getElements())){
            ProjectElements elements = new ProjectElements();
            BeanUtil.copyProperties(resources.getElements(),elements);
            // 手动处理枚举字段转换
            convertProjectElementsDtoToEntity(resources.getElements(), elements);

            String elemId = "ELEM" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
            elements.setId(elemId);
            elements.setProjectCode(info.getProjectCode());
            elements.setCreatedBy(userId);
            elements.setCreatedTime(dateTime);
            projectElementsMapper.insert(elements);

            if(ObjectUtil.isNotEmpty(resources.getElementsExt())){
                ProjectElementsExt elementsExt = new ProjectElementsExt();
                BeanUtil.copyProperties(resources.getElementsExt(),elementsExt);
                // 手动处理枚举字段转换
                convertProjectElementsExtDtoToEntity(resources.getElementsExt(), elementsExt);

                String extId = "EXT" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
                elementsExt.setId(extId);
                elementsExt.setParentId(elemId);
                elementsExt.setProjectCode(info.getProjectCode());
                elementsExt.setCreatedBy(userId);
                elementsExt.setCreatedTime(dateTime);
                projectElementsExtMapper.insert(elementsExt);
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectInfoDto resources) {
        String userId = String.valueOf(SecurityUtils.getCurrentUserId());
        LocalDateTime dateTime = LocalDateTime.now();
        ProjectInfo projectInfo = projectInfoMapper.selectById(resources.getId());
        BeanUtil.copyProperties(resources,projectInfo);
        // 手动处理枚举字段转换
        convertProjectInfoDtoToEntity(resources, projectInfo);

        projectInfo.setUpdatedBy(userId);
        projectInfo.setUpdatedTime(dateTime);
        projectInfoMapper.updateById(projectInfo);

        if(ObjectUtil.isNotEmpty(resources.getElements())){
            ProjectElements elements = this.getProjectElements(resources.getProjectCode());
            BeanUtil.copyProperties(resources.getElements(),elements);
            // 手动处理枚举字段转换
            convertProjectElementsDtoToEntity(resources.getElements(), elements);

            elements.setUpdatedBy(userId);
            elements.setUpdatedTime(dateTime);
            projectElementsMapper.updateById(elements);

            if(ObjectUtil.isNotEmpty(resources.getElementsExt())){
                ProjectElementsExt projectElementExt = this.getProjectElementExt(elements.getId());
                BeanUtil.copyProperties(resources.getElementsExt(),projectElementExt);
                // 手动处理枚举字段转换
                convertProjectElementsExtDtoToEntity(resources.getElementsExt(), projectElementExt);

                projectElementExt.setUpdatedBy(userId);
                projectElementExt.setUpdatedTime(dateTime);
                projectElementsExtMapper.updateById(projectElementExt);
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<String> ids) {
        projectInfoMapper.deleteBatchIds(ids);
        List<ProjectInfo> projects = this.getProjects(ids);
        Set<String> collect = projects.stream().map(ProjectInfo::getProjectCode).collect(Collectors.toSet());
        List<ProjectElements> projectElements = getProjectElements(collect);
        Set<String> collect1 = projectElements.stream().map(ProjectElements::getId).collect(Collectors.toSet());
        projectElementsMapper.deleteBatchIds(collect1);
        this.delProjectElementExt(collect1);
    }

    @Override
    public ProjectInfoVo getProjectInfo(String id) {
        ProjectInfoVo vo = new ProjectInfoVo();
        ProjectInfo info = projectInfoMapper.selectById(id);
        BeanUtil.copyProperties(info,vo);
        // 手动处理枚举字段转换
        convertProjectInfoToVo(info, vo);

        ProjectElementsVo elementsVo = new ProjectElementsVo();
        ProjectElements projectElements = getProjectElements(info.getProjectCode());
        BeanUtil.copyProperties(projectElements,elementsVo);
        // 手动处理枚举字段转换
        convertProjectElementsToVo(projectElements, elementsVo);
        vo.setElements(elementsVo);

        ProjectElementsExtVo elementsExtVo = new ProjectElementsExtVo();
        ProjectElementsExt projectElementExt = getProjectElementExt(projectElements.getId());
        BeanUtil.copyProperties(projectElementExt,elementsExtVo);
        // 手动处理枚举字段转换
        convertProjectElementsExtToVo(projectElementExt, elementsExtVo);
        vo.setElementsExt(elementsExtVo);
        return vo;
    }

    @Override
    public void download(List<ProjectInfo> all, HttpServletResponse response) throws IOException {
    }


    private void delProjectElementExt(Set<String> collect){
        LambdaQueryWrapper<ProjectElementsExt> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProjectElementsExt::getParentId,collect);
        projectElementsExtMapper.delete(lambdaQueryWrapper);
    }

    private List<ProjectElements> getProjectElements(Set<String> collect){
        LambdaQueryWrapper<ProjectElements> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProjectElements::getProjectCode,collect);
        return projectElementsMapper.selectList(lambdaQueryWrapper);
    }

    private List<ProjectInfo> getProjects(List<String> ids){
        LambdaQueryWrapper<ProjectInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProjectInfo::getId,ids);
        return projectInfoMapper.selectList(lambdaQueryWrapper);
    }

    private void checkProjectCode(String projectCode){
        LambdaQueryWrapper<ProjectInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectInfo::getProjectCode,projectCode);
        List<ProjectInfo> projectInfos = projectInfoMapper.selectList(lambdaQueryWrapper);
        if(ObjectUtil.isNotEmpty(projectInfos)){
            throw new RuntimeException("项目编码已重复");
        }
    }

    private ProjectElements getProjectElements(String projectCode){
        LambdaQueryWrapper<ProjectElements> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectElements::getProjectCode,projectCode);
        ProjectElements elements = projectElementsMapper.selectOne(lambdaQueryWrapper);
        if(ObjectUtil.isNotEmpty(elements)){
            return elements;
        }
        return null;
    }


    private ProjectElementsExt getProjectElementExt(String id){
        LambdaQueryWrapper<ProjectElementsExt> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectElementsExt::getParentId,id);
        ProjectElementsExt elementsExt = projectElementsExtMapper.selectOne(lambdaQueryWrapper);
        if(ObjectUtil.isNotEmpty(elementsExt)){
            return elementsExt;
        }
        return null;
    }

}
