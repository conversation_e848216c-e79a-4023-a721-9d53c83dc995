package com.jinghang.cash.modules.project.service.domain.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.api.dto.ProjectElementsDto;
import com.jinghang.cash.api.dto.ProjectElementsExtDto;
import com.jinghang.cash.api.enums.AbleStatus;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 项目信息实体
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 10:41
 */
@Data
public class ProjectInfoVo implements Serializable {


    /**
     * 主键ID
     */
    private String id;

    /**
     * 项目唯一编码
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 资产方编码 (关联资产方表)
     */
    private String flowChannel;

    private String flowChannelName;

    /**
     * 融担方编码 (关联融担方表)
     */
    private String guaranteeCode;

    private String guaranteeCodeName;

    /**
     * 资金方编码 (关联资金方表)
     */
    private String capitalChannel;

    private String capitalChannelName;

    /**
     * 项目类型编码 (关联项目类型表)
     */
    private String projectTypeCode;

    private String projectTypeCodeName;



    /**
     * 日授信限额(万元)
     */
    private BigDecimal dailyCreditLimit;

    /**
     * 日放款限额(万元)
     */
    private BigDecimal dailyLoanLimit;


    /**
     * 项目状态 (ENABLE/DISABLE)
     */
    private AbleStatus enabled;

    /**
     * 项目开始日期
     */
    @TableField(value = "start_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startDate;

    /**
     * 项目结束日期
     */
    @TableField(value = "end_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endDate;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 版本号
     */
    @TableField(value = "revision", fill = FieldFill.INSERT)
    @Version
    private Integer revision;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.UPDATE)
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;


    /**
     * 项目要素信息
     */
    private ProjectElementsVo elements;

    /**
     * 项目要素扩展信息
     */
    private ProjectElementsExtVo elementsExt;
}
