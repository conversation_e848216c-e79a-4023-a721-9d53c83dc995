/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghang.cash.modules.project.domain.ProjectElements;
import com.jinghang.cash.utils.PageResult;
import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.modules.project.service.ProjectElementsService;
import com.jinghang.cash.modules.project.domain.dto.ProjectElementsQueryCriteria;
import com.jinghang.cash.modules.project.mapper.ProjectElementsMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @description 服务实现
* <AUTHOR>
* @date 2025-08-25
**/
@Service
@RequiredArgsConstructor
public class ProjectElementsServiceImpl extends ServiceImpl<ProjectElementsMapper, ProjectElements> implements ProjectElementsService {

    private final ProjectElementsMapper projectElementsMapper;

    @Override
    public PageResult<ProjectElements> queryAll(ProjectElementsQueryCriteria criteria, Page<Object> page){
        //return PageUtil.toPage(projectElementsMapper.findAll(criteria, page));
        return null;
    }

    @Override
    public List<ProjectElements> queryAll(ProjectElementsQueryCriteria criteria){
        LambdaQueryWrapper<ProjectElements> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(criteria.getProjectCode())){
            lambdaQueryWrapper.eq(ProjectElements::getProjectCode,criteria.getProjectCode());
        }
        return projectElementsMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public ProjectElements queryProjectElementsInfo(String projectCode) {
        LambdaQueryWrapper<ProjectElements> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(projectCode)){
            lambdaQueryWrapper.eq(ProjectElements::getProjectCode,projectCode);
        }
        return projectElementsMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProjectElements resources) {
        projectElementsMapper.insert(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectElements resources) {
        ProjectElements projectElements = getById(resources.getId());
        //projectElements.copy(resources);
        projectElementsMapper.updateById(projectElements);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<String> ids) {
        projectElementsMapper.deleteBatchIds(ids);
    }

    @Override
    public void download(List<ProjectElements> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectElements projectElements : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("关联的项目唯一编码", projectElements.getProjectCode());
            map.put("可提现范围(元) (格式 如 1000-50000)", projectElements.getDrawableAmountRange());
            map.put("单笔提现步长(元)", projectElements.getDrawableAmountStep());
            map.put("授信黑暗期 (格式 HH:mm-HH:mm)", projectElements.getCreditDarkHours());
            map.put("用信黑暗期 (格式 HH:mm-HH:mm)", projectElements.getLoanDarkHours());
            map.put("还款黑暗期 (格式 HH:mm-HH:mm)", projectElements.getRepayDarkHours());
            map.put("资金方授信黑暗期", projectElements.getFundingCreditDarkHours());
            map.put("资金方用信黑暗期", projectElements.getFundingLoanDarkHours());
            map.put("资金方还款黑暗期", projectElements.getFundingRepayDarkHours());
            map.put("日授信限额(万元)", projectElements.getDailyCreditLimit());
            map.put("日放款限额(万元)", projectElements.getDailyLoanLimit());
            map.put("授信锁定期限(天)", projectElements.getCreditLockDays());
            map.put("用信锁定期限(天)", projectElements.getLoanLockDays());
            map.put("对客利率(%)", projectElements.getCustomerInterestRate());
            map.put("对资利率(%)", projectElements.getFundingInterestRate());
            map.put("年龄范围(岁) (格式 如 22-55 左右包含)", projectElements.getAgeRange());
            map.put("支持的还款类型 (英文逗号分隔)", projectElements.getSupportedRepayTypes());
            map.put("借款期限 (英文逗号分隔)", projectElements.getLoanTerms());
            map.put("资方路由", projectElements.getCapitalRoute());
            //map.put("备注", projectElements.getRemark());
            map.put("乐观锁", projectElements.getRevision());
            map.put("创建人", projectElements.getCreatedBy());
            map.put("创建时间", projectElements.getCreatedTime());
            map.put("更新人", projectElements.getUpdatedBy());
            map.put("更新时间", projectElements.getUpdatedTime());
            map.put("项目时效类型（LONGTIME, TEMPORARY）", projectElements.getProjectDurationType());
            map.put("临时配置有效期起", projectElements.getTempStartTime());
            map.put("临时配置有效期止", projectElements.getTempEndTime());
            map.put("启用状态", projectElements.getEnabled());
            map.put("是否年结", projectElements.getGraceNext());
            list.add(map);
        }
        //FileUtil.downloadExcel(list, response);
    }
}