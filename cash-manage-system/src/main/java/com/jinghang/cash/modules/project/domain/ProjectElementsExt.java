/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.domain;

import com.jinghang.cash.api.enums.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* @description /
* <AUTHOR>
* @date 2025-08-25
**/
@Data
@TableName("project_elements_ext")
public class ProjectElementsExt implements Serializable {

    @TableId(value = "id")
    @ApiModelProperty(value = "主键")
    private String id;

    @NotBlank
    @ApiModelProperty(value = "项目要素主键ID")
    private String parentId;

    @NotBlank
    @ApiModelProperty(value = "关联的项目唯一编码")
    private String projectCode;

    @ApiModelProperty(value = "年利率基数(天) (如 360或365)")
    private String interestDaysBasis;

    @ApiModelProperty(value = "是否支持线下跨日还款")
    private ActiveInactive allowCrossDayRepay;

    @ApiModelProperty(value = "风控模型渠道")
    private RiskModelChannel riskModelChannel;

    @ApiModelProperty(value = "放款支付渠道")
    private LoanPayChannel loanPaymentChannel;

    @ApiModelProperty(value = "扣款绑卡渠道")
    private DeductChannel deductionBindCardChannel;

    @ApiModelProperty(value = "扣款商户号")
    private String deductionMerchantCode;

    @ApiModelProperty(value = "签章渠道")
    private SignChannel signChannel;

    @ApiModelProperty(value = "逾期短信发送方")
    private String overdueSmsSender;

    @ApiModelProperty(value = "短信渠道")
    private SmsChannel smsChannel;

    @ApiModelProperty(value = "逾期宽限期类型 (SQ:首期, MQ:每期)")
    private GracePeriodType gracePeriodType;

    @ApiModelProperty(value = "逾期宽限期(天)")
    private String gracePeriodDays;

    @ApiModelProperty(value = "节假日是否顺延")
    private String holidayPostpone;

    @ApiModelProperty(value = "征信查询方")
    private String creditQueryParty;

    @ApiModelProperty(value = "征信上报方")
    private String creditReportSender;

    @ApiModelProperty(value = "催收方")
    private String collectionParty;

    @ApiModelProperty(value = "是否推送催收数据")
    private ActiveInactive pushCollectionData;

    @ApiModelProperty(value = "是否推送客诉数据")
    private ActiveInactive pushKsData;

    @ApiModelProperty(value = "是否支持催收减免")
    private ActiveInactive allowCollectionWaiver;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "乐观锁")
    private String revision;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

    public void copy(ProjectElementsExt source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
