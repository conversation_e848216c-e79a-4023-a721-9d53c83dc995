/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.rest;

import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.project.domain.ProjectElementsExt;
import com.jinghang.cash.modules.project.service.ProjectElementsExtService;
import com.jinghang.cash.modules.project.domain.dto.ProjectElementsExtQueryCriteria;
import com.jinghang.cash.utils.PageResult;
import lombok.RequiredArgsConstructor;
import java.util.List;

import org.springframework.data.repository.query.Param;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
* <AUTHOR>
* @date 2025-08-25
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "project")
@RequestMapping("/api/projectElementsExt")
public class ProjectElementsExtController {

    private final ProjectElementsExtService projectElementsExtService;

    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('projectElementsExt:list')")
    public void exportProjectElementsExt(HttpServletResponse response, ProjectElementsExtQueryCriteria criteria) throws IOException {
        projectElementsExtService.download(projectElementsExtService.queryAll(criteria), response);
    }

    @GetMapping("/listPage")
    @ApiOperation("查询project")
    @PreAuthorize("@el.check('projectElementsExt:list')")
    public RestResult<PageResult<ProjectElementsExt>> queryProjectElementsExt(ProjectElementsExtQueryCriteria criteria){
        Page<Object> page = new Page<>(criteria.getPage(), criteria.getSize());
        return RestResult.success(projectElementsExtService.queryAll(criteria,page));
    }

    @GetMapping("/info")
    @PreAuthorize("@el.check('projectElementsExt:info')")
    public RestResult<ProjectElementsExt> queryProjectElementsExtInfo(@RequestParam("id")String id){
        return RestResult.success(projectElementsExtService.queryProjectElementsExtInfo(id));
    }


    @PostMapping("/add")
    @ApiOperation("新增project")
    @PreAuthorize("@el.check('projectElementsExt:add')")
    public RestResult<Object> createProjectElementsExt(@Validated @RequestBody ProjectElementsExt resources){
        projectElementsExtService.create(resources);
        return RestResult.success();
    }

    @PostMapping("/edit")
    @ApiOperation("修改project")
    @PreAuthorize("@el.check('projectElementsExt:edit')")
    public RestResult<Object> updateProjectElementsExt(@Validated @RequestBody ProjectElementsExt resources){
        projectElementsExtService.update(resources);
        return RestResult.success();
    }

    @PostMapping("/del")
    @ApiOperation("删除project")
    @PreAuthorize("@el.check('projectElementsExt:del')")
    public RestResult<Object> deleteProjectElementsExt(@ApiParam(value = "传ID数组[]") @RequestBody List<String> ids) {
        projectElementsExtService.deleteAll(ids);
        return RestResult.success();
    }
}