/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.rest;

import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.project.domain.ProjectContract;
import com.jinghang.cash.modules.project.service.ProjectContractService;
import com.jinghang.cash.modules.project.domain.dto.ProjectContractQueryCriteria;
import lombok.RequiredArgsConstructor;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2025-08-21
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "合同")
@RequestMapping("/api/projectContract")
public class ProjectContractController {

    private final ProjectContractService projectContractService;

    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('projectContract:list')")
    public void exportProjectContractConfig(HttpServletResponse response, ProjectContractQueryCriteria criteria) throws IOException {
        projectContractService.download(projectContractService.queryAll(criteria), response);
    }

    @GetMapping("/page")
    @ApiOperation("查询合同")
    @PreAuthorize("@el.check('projectContract:list')")
    public RestResult queryProjectContractConfig(ProjectContractQueryCriteria criteria){
        return RestResult.success(projectContractService.queryPage(criteria));
    }

    @PostMapping("/create")
    @ApiOperation("新增合同")
    @PreAuthorize("@el.check('projectContract:add')")
    public RestResult<Object> createProjectContractConfig(@Validated @RequestBody ProjectContract resources){
        projectContractService.create(resources);
        return RestResult.success(HttpStatus.CREATED);
    }

    @PostMapping("/update")
    @ApiOperation("修改合同")
    @PreAuthorize("@el.check('projectContract:edit')")
    public RestResult<Object> updateProjectContractConfig(@Validated @RequestBody ProjectContract resources){
        projectContractService.update(resources);
        return RestResult.success(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/del")
    @ApiOperation("删除合同")
    @PreAuthorize("@el.check('projectContract:del')")
    public RestResult<Object> deleteProjectContractConfig(@ApiParam(value = "传ID数组[]") @RequestBody List<String> ids) {
        projectContractService.deleteAll(ids);
        return RestResult.success(HttpStatus.OK);
    }
}