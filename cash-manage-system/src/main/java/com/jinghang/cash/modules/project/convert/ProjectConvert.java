package com.jinghang.cash.modules.project.convert;

import com.jinghang.cash.api.dto.ProjectElementsDto;
import com.jinghang.cash.api.dto.ProjectElementsExtDto;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.api.enums.*;
import com.jinghang.cash.modules.project.domain.ProjectElements;
import com.jinghang.cash.modules.project.domain.ProjectElementsExt;
import com.jinghang.cash.modules.project.domain.ProjectInfo;
import com.jinghang.cash.modules.project.service.domain.vo.ProjectElementsExtVo;
import com.jinghang.cash.modules.project.service.domain.vo.ProjectElementsVo;
import com.jinghang.cash.modules.project.service.domain.vo.ProjectInfoVo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 项目相关对象转换器
 *
 * @Author: Lior
 * @CreateTime: 2025/8/28
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProjectConvert {
    
    ProjectConvert INSTANCE = Mappers.getMapper(ProjectConvert.class);

    // ==================== Entity 到 DTO 转换 ====================
    
    /**
     * ProjectInfo Entity 转 DTO
     */
    ProjectInfoDto toDto(ProjectInfo entity);
    
    /**
     * ProjectElements Entity 转 DTO
     */
    ProjectElementsDto toDto(ProjectElements entity);
    
    /**
     * ProjectElementsExt Entity 转 DTO
     */
    @Mapping(source = "allowCrossDayRepay", target = "allowCrossDayRepay", qualifiedByName = "stringToActiveInactive")
    @Mapping(source = "riskModelChannel", target = "riskModelChannel", qualifiedByName = "stringToRiskModelChannel")
    @Mapping(source = "loanPaymentChannel", target = "loanPaymentChannel", qualifiedByName = "stringToLoanPayChannel")
    @Mapping(source = "deductionBindCardChannel", target = "deductionBindCardChannel", qualifiedByName = "stringToDeductChannel")
    @Mapping(source = "signChannel", target = "signChannel", qualifiedByName = "stringToSignChannel")
    @Mapping(source = "smsChannel", target = "smsChannel", qualifiedByName = "stringToSmsChannel")
    @Mapping(source = "gracePeriodType", target = "gracePeriodType", qualifiedByName = "stringToGracePeriodType")
    ProjectElementsExtDto toDto(ProjectElementsExt entity);

    // ==================== DTO 到 Entity 转换 ====================
    
    /**
     * ProjectInfoDto 转 Entity
     */
    ProjectInfo toEntity(ProjectInfoDto dto);
    
    /**
     * ProjectElementsDto 转 Entity
     */
    ProjectElements toEntity(ProjectElementsDto dto);
    
    /**
     * ProjectElementsExtDto 转 Entity
     */
    @Mapping(source = "allowCrossDayRepay", target = "allowCrossDayRepay", qualifiedByName = "activeInactiveToString")
    @Mapping(source = "riskModelChannel", target = "riskModelChannel", qualifiedByName = "riskModelChannelToString")
    @Mapping(source = "loanPaymentChannel", target = "loanPaymentChannel", qualifiedByName = "loanPayChannelToString")
    @Mapping(source = "deductionBindCardChannel", target = "deductionBindCardChannel", qualifiedByName = "deductChannelToString")
    @Mapping(source = "signChannel", target = "signChannel", qualifiedByName = "signChannelToString")
    @Mapping(source = "smsChannel", target = "smsChannel", qualifiedByName = "smsChannelToString")
    @Mapping(source = "gracePeriodType", target = "gracePeriodType", qualifiedByName = "gracePeriodTypeToString")
    ProjectElementsExt toEntity(ProjectElementsExtDto dto);

    // ==================== Entity 到 VO 转换 ====================
    
    /**
     * ProjectInfo Entity 转 VO
     */
    ProjectInfoVo toVo(ProjectInfo entity);
    
    /**
     * ProjectElements Entity 转 VO
     */
    ProjectElementsVo toVo(ProjectElements entity);
    
    /**
     * ProjectElementsExt Entity 转 VO
     */
    @Mapping(source = "allowCrossDayRepay", target = "allowCrossDayRepay", qualifiedByName = "stringToActiveInactive")
    @Mapping(source = "riskModelChannel", target = "riskModelChannel", qualifiedByName = "stringToRiskModelChannel")
    @Mapping(source = "loanPaymentChannel", target = "loanPaymentChannel", qualifiedByName = "stringToLoanPayChannel")
    @Mapping(source = "deductionBindCardChannel", target = "deductionBindCardChannel", qualifiedByName = "stringToDeductChannel")
    @Mapping(source = "signChannel", target = "signChannel", qualifiedByName = "stringToSignChannel")
    @Mapping(source = "smsChannel", target = "smsChannel", qualifiedByName = "stringToSmsChannel")
    @Mapping(source = "gracePeriodType", target = "gracePeriodType", qualifiedByName = "stringToGracePeriodType")
    ProjectElementsExtVo toVo(ProjectElementsExt entity);

    // ==================== 枚举转换方法 ====================
    
    /**
     * 字符串转 ActiveInactive 枚举
     */
    @Named("stringToActiveInactive")
    static ActiveInactive stringToActiveInactive(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return ActiveInactive.valueOf(value);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * ActiveInactive 枚举转字符串
     */
    @Named("activeInactiveToString")
    static String activeInactiveToString(ActiveInactive value) {
        return value != null ? value.name() : null;
    }
    
    /**
     * 字符串转 RiskModelChannel 枚举
     */
    @Named("stringToRiskModelChannel")
    static RiskModelChannel stringToRiskModelChannel(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return RiskModelChannel.valueOf(value);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * RiskModelChannel 枚举转字符串
     */
    @Named("riskModelChannelToString")
    static String riskModelChannelToString(RiskModelChannel value) {
        return value != null ? value.name() : null;
    }
    
    /**
     * 字符串转 LoanPayChannel 枚举
     */
    @Named("stringToLoanPayChannel")
    static LoanPayChannel stringToLoanPayChannel(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return LoanPayChannel.valueOf(value);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * LoanPayChannel 枚举转字符串
     */
    @Named("loanPayChannelToString")
    static String loanPayChannelToString(LoanPayChannel value) {
        return value != null ? value.name() : null;
    }
    
    /**
     * 字符串转 DeductChannel 枚举
     */
    @Named("stringToDeductChannel")
    static DeductChannel stringToDeductChannel(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return DeductChannel.valueOf(value);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * DeductChannel 枚举转字符串
     */
    @Named("deductChannelToString")
    static String deductChannelToString(DeductChannel value) {
        return value != null ? value.name() : null;
    }
    
    /**
     * 字符串转 SignChannel 枚举
     */
    @Named("stringToSignChannel")
    static SignChannel stringToSignChannel(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return SignChannel.valueOf(value);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * SignChannel 枚举转字符串
     */
    @Named("signChannelToString")
    static String signChannelToString(SignChannel value) {
        return value != null ? value.name() : null;
    }
    
    /**
     * 字符串转 SmsChannel 枚举
     */
    @Named("stringToSmsChannel")
    static SmsChannel stringToSmsChannel(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return SmsChannel.valueOf(value);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * SmsChannel 枚举转字符串
     */
    @Named("smsChannelToString")
    static String smsChannelToString(SmsChannel value) {
        return value != null ? value.name() : null;
    }
    
    /**
     * 字符串转 GracePeriodType 枚举
     */
    @Named("stringToGracePeriodType")
    static GracePeriodType stringToGracePeriodType(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return GracePeriodType.valueOf(value);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * GracePeriodType 枚举转字符串
     */
    @Named("gracePeriodTypeToString")
    static String gracePeriodTypeToString(GracePeriodType value) {
        return value != null ? value.name() : null;
    }
}
