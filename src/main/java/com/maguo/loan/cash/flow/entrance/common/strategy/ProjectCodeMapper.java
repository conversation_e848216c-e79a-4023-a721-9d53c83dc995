package com.maguo.loan.cash.flow.entrance.common.strategy;

import com.maguo.loan.cash.flow.job.jh.RepayPlanJhJob;
import com.maguo.loan.cash.flow.repository.ProjectProductMappingRepository;
import com.maguo.loan.cash.flow.service.CacheService;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ProjectCodeMapper {

    private static final Logger logger = LoggerFactory.getLogger(RepayPlanJhJob.class);

    private final List<ProductCodeStrategy> strategies;

    @Autowired
    private ProductCodeStrategyFactory strategyFactory;

    @Autowired
    private  CacheService cacheService;

    @Autowired
    public ProjectCodeMapper(List<ProductCodeStrategy> strategies) {
        this.strategies = strategies;
    }

    @Autowired
    private ProjectProductMappingRepository projectProductMappingRepository;

    private final Map<String, String> mappingCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        logger.info("================= ProjectCodeMapper 诊断信息 =================");
        if (this.strategies == null || this.strategies.isEmpty()) {
            logger.error("【严重】: strategies 列表为空或null！没有注入任何策略Bean！");
        } else {
            logger.info("成功注入了 {} 个策略Bean，列表如下:", this.strategies.size());
            this.strategies.forEach(strategy -> {
                logger.info(" -> 已加载策略: {}", strategy.getClass().getName());
            });
        }
        logger.info("==============================================================");
    }


    /**
     * @param source 映射来源
     * @param params 包含所有相关特征的键值对
     * @return 匹配到的 project_code
     */
    public String getProjectCode(String source, Map<String, String> params) {
        // 调用工厂的统一方法，所有复杂的路由和逻辑判断都被封装起来了
        return strategyFactory.getProductCode(source, params);
    }


}
