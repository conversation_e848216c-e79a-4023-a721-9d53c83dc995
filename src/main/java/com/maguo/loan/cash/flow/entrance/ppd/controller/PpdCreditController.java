package com.maguo.loan.cash.flow.entrance.ppd.controller;

import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.entrance.common.strategy.ProjectCodeMapper;
import com.maguo.loan.cash.flow.entrance.ppd.dto.ApiResult;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.CreditApplyRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.CreditQueryRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.res.CreditApplyResponse;
import com.maguo.loan.cash.flow.entrance.ppd.exception.PpdBizException;
import com.maguo.loan.cash.flow.entrance.ppd.service.PpdService;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.service.CreditValidationService;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;


@RestController
@RequestMapping("/ppd/api")
public class PpdCreditController extends PpdApiValidator {

    private static final Logger logger = LoggerFactory.getLogger(PpdCreditController.class);

    @Autowired
    private PpdService ppdService;

    @Autowired
    private CreditValidationService creditValidationService;

    @Autowired
    private ProjectCodeMapper projectCodeMapper;

    @Autowired
    private ProjectInfoService projectInfoService;

    @PostMapping("/creditApply")
    public ApiResult creditApply(@RequestBody @Valid CreditApplyRequest request, BindingResult bindingResult) throws PpdBizException {
        // 必填参数校验
        validate(bindingResult);
        // 公共参数校验
        String validationMessage =  validateCommonParameters(request);
        if (StringUtils.isNotBlank(validationMessage)) {
            logger.warn("授信申请 pre-check 失败, 原因: {}", validationMessage);

            CreditApplyResponse response = new CreditApplyResponse();
            response.setStatus("01");
            response.setMsg(validationMessage);
            return response;
        }
        // 业务逻辑
        return ppdService.approval(request);
    }

    /**
     * 公共参数校验
     */
    private String validateCommonParameters(CreditApplyRequest request) {
        // 获取项目编码
        String projectCode = getProjectCode(request);
        ProjectInfoDto projectElements = projectInfoService.queryProjectInfo(projectCode);
        if (projectElements == null){
            return "未配置项目信息";
        }
        if (StringUtils.isNotBlank(projectCode)) {
            // 校验授信黑暗期
            if (creditValidationService.isInCreditDarkHours(projectElements)) {
                return "当前时间在授信黑暗期内，请稍后再试";
            }
            // 校验日授信限额
            if (creditValidationService.isDailyCreditLimitExceeded(projectElements, request.getLoanAmt())) {
                return "日授信限额超限";
            }

            // 校验年龄范围
            if (!creditValidationService.isAgeInRange(projectElements, request.getAge())) {
                return "年龄不在允许范围内";
            }

            // 校验可提现范围
            if (!creditValidationService.isAmountInDrawableRange(projectElements, request.getLoanAmt())) {
                return "申请金额不在可提现范围内";
            }

        }

        // 基础校验
        return validateBasicParameters(request);
    }

    /**
     * 基础参数校验
     */
    private String  validateBasicParameters(CreditApplyRequest request) {
        BigDecimal loanAmt = request.getLoanAmt();
        if(loanAmt == null){
            return "放款金额不能为空。";
        }

        String idExpiryDate = request.getIdExpiryDate();
        LocalDate expiryDate = LocalDate.parse(idExpiryDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        if(LocalDate.now().isAfter(expiryDate)) {
            return "身份证已过期。";
        }


        ApplyChannel applyChannel = ApplyChannel.getApplyChannel(request.getSourceCode());
        if (applyChannel == null) {
            return "未知渠道号。";
        }
        return null;
    }

    /**
     * 获取项目编码
     */
    private String getProjectCode(CreditApplyRequest request) {
        Map<String, String> params = new HashMap<>();
        params.put("flowSource", FlowChannel.PPCJDL.name());
        params.put("sourceCode", request.getSourceCode());
        params.put("accessType", request.getAccessType());
        String projectCode = projectCodeMapper.getProjectCode("FLOW", params);

        return projectCode;
    }

    @PostMapping("/creditQuery")
    public ApiResult creditQuery(@RequestBody @Valid CreditQueryRequest request, BindingResult bindingResult) throws PpdBizException {

        // 必填参数校验
        validate(bindingResult);
        //验证
        ApplyChannel applyChannel = ApplyChannel.getApplyChannel(request.getSourceCode());
        if (applyChannel == null) {
            throw new PpdBizException("未知渠道号。");
        }
        // 业务逻辑
        return ppdService.creditResultQuery(request);
    }
}
