package com.maguo.loan.cash.flow.entrance.common.strategy.fql;

import com.maguo.loan.cash.flow.entity.common.ProjectProductMapping;
import com.maguo.loan.cash.flow.entrance.common.strategy.ProductCodeStrategy;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.repository.ProjectProductMappingRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

@Component
public class FqlFlowStrategy implements ProductCodeStrategy {

    @Autowired
    private ProjectProductMappingRepository projectProductMappingRepository;

    @Override
    public boolean supports(String source, Map<String, String> params) {
        return "FLOW".equalsIgnoreCase(source) &&
            FlowChannel.FQLQY001.name().equalsIgnoreCase(params.get("flowSource"));
    }

    @Override
    public String buildProductCode(Map<String, String> params) {
        String partnerCode = params.get("partnerCode");
        if (partnerCode == null || partnerCode.isEmpty()) {
            throw new IllegalArgumentException("分期乐流量映射必须包含 'partnerCode' 参数");
        }
        Optional<ProjectProductMapping> mapping = projectProductMappingRepository.findByProductCode(partnerCode);
        return mapping.map(ProjectProductMapping::getProjectCode).orElse(null);
    }
}
