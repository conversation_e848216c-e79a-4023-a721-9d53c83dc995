package com.maguo.loan.cash.flow.entrance.common.strategy.lvxin;

import com.maguo.loan.cash.flow.entity.common.ProjectProductMapping;
import com.maguo.loan.cash.flow.entrance.common.strategy.ProductCodeStrategy;
import com.maguo.loan.cash.flow.entrance.common.strategy.ppd.PpdFlowStrategy;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.repository.ProjectProductMappingRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

@Component
public class LvxinFlowStrategy implements ProductCodeStrategy {
    private static final Logger logger = LoggerFactory.getLogger(PpdFlowStrategy.class);

    @Autowired
    private ProjectProductMappingRepository projectProductMappingRepository;

    @Override
    public boolean supports(String flowSource, Map<String, String> params) {
        return "FLOW".equalsIgnoreCase(flowSource) &&
            FlowChannel.LVXIN.name().equalsIgnoreCase(params.get("flowSource"));
    }

    @Override
    public String buildProductCode(Map<String, String> params) {
        String productType = params.get("productType");
        if (productType == null) {
            throw new IllegalArgumentException("绿信流量映射必须包含 'productType' 参数");
        }

        logger.info("------Lvxin----"+productType);
        Optional<ProjectProductMapping> mapping = projectProductMappingRepository.findByProductCode(productType);
        return mapping.map(ProjectProductMapping::getProjectCode).orElse(null);
    }
}
