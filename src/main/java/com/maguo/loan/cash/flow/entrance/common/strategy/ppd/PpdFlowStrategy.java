package com.maguo.loan.cash.flow.entrance.common.strategy.ppd;

import com.maguo.loan.cash.flow.entity.common.ProjectProductMapping;
import com.maguo.loan.cash.flow.entrance.common.strategy.ProductCodeStrategy;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.repository.ProjectProductMappingRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

@Component
public class PpdFlowStrategy implements ProductCodeStrategy {

    private static final Logger logger = LoggerFactory.getLogger(PpdFlowStrategy.class);

    @Autowired
    private  ProjectProductMappingRepository projectProductMappingRepository;

    @Override
    public boolean supports(String source, Map<String, String> params) {
        return "FLOW".equalsIgnoreCase(source) &&
            FlowChannel.PPCJDL.name().equalsIgnoreCase(params.get("flowSource"));
    }

    @Override
    public String buildProductCode(Map<String, String> params) {
        String sourceCode = params.get("sourceCode");
        String accessType = params.get("accessType");
        if (sourceCode == null || accessType == null) {
            throw new IllegalArgumentException("拍拍贷流量映射必须包含 'sourceCode' 和 'accessType' 参数");
        }
        String product = sourceCode + "_" + accessType;
        logger.info("------PPD----"+product);
        Optional<ProjectProductMapping> mapping = projectProductMappingRepository.findByProductCode(product);
        return mapping.map(ProjectProductMapping::getProjectCode).orElse(null);
    }
}
