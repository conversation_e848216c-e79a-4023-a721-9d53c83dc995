# 枚举字段拷贝问题解决方案 - MapStruct 版本

## 问题描述

在 `ProjectInfoServiceImpl` 中使用 `BeanUtils.copyProperties` 方法拷贝对象时，枚举字段没有正确拷贝的问题。

### 根本原因

1. **实体类（Entity）** 中的枚举字段在数据库中存储为 **字符串（String）**
2. **DTO类** 中对应的字段是 **枚举类型（Enum）**
3. **VO类** 中对应的字段也是 **枚举类型（Enum）**
4. `BeanUtils.copyProperties` 无法自动处理字符串到枚举的转换

## 解决方案

### 1. 创建 MapStruct 转换器

按照系统惯例，使用 MapStruct 创建专门的转换器类 `ProjectConvert`：

**文件位置**: `cash-manage-system/src/main/java/com/jinghang/cash/modules/project/convert/ProjectConvert.java`

```java
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProjectConvert {
    
    ProjectConvert INSTANCE = Mappers.getMapper(ProjectConvert.class);

    // ==================== Entity 到 DTO 转换 ====================
    
    ProjectInfoDto toDto(ProjectInfo entity);
    ProjectElementsDto toDto(ProjectElements entity);
    
    @Mapping(source = "allowCrossDayRepay", target = "allowCrossDayRepay", qualifiedByName = "stringToActiveInactive")
    @Mapping(source = "riskModelChannel", target = "riskModelChannel", qualifiedByName = "stringToRiskModelChannel")
    @Mapping(source = "loanPaymentChannel", target = "loanPaymentChannel", qualifiedByName = "stringToLoanPayChannel")
    @Mapping(source = "deductionBindCardChannel", target = "deductionBindCardChannel", qualifiedByName = "stringToDeductChannel")
    @Mapping(source = "signChannel", target = "signChannel", qualifiedByName = "stringToSignChannel")
    @Mapping(source = "smsChannel", target = "smsChannel", qualifiedByName = "stringToSmsChannel")
    @Mapping(source = "gracePeriodType", target = "gracePeriodType", qualifiedByName = "stringToGracePeriodType")
    ProjectElementsExtDto toDto(ProjectElementsExt entity);

    // ==================== DTO 到 Entity 转换 ====================
    
    ProjectInfo toEntity(ProjectInfoDto dto);
    ProjectElements toEntity(ProjectElementsDto dto);
    
    @Mapping(source = "allowCrossDayRepay", target = "allowCrossDayRepay", qualifiedByName = "activeInactiveToString")
    @Mapping(source = "riskModelChannel", target = "riskModelChannel", qualifiedByName = "riskModelChannelToString")
    @Mapping(source = "loanPaymentChannel", target = "loanPaymentChannel", qualifiedByName = "loanPayChannelToString")
    @Mapping(source = "deductionBindCardChannel", target = "deductionBindCardChannel", qualifiedByName = "deductChannelToString")
    @Mapping(source = "signChannel", target = "signChannel", qualifiedByName = "signChannelToString")
    @Mapping(source = "smsChannel", target = "smsChannel", qualifiedByName = "smsChannelToString")
    @Mapping(source = "gracePeriodType", target = "gracePeriodType", qualifiedByName = "gracePeriodTypeToString")
    ProjectElementsExt toEntity(ProjectElementsExtDto dto);

    // ==================== Entity 到 VO 转换 ====================
    
    ProjectInfoVo toVo(ProjectInfo entity);
    ProjectElementsVo toVo(ProjectElements entity);
    ProjectElementsExtVo toVo(ProjectElementsExt entity);

    // ==================== 枚举转换方法 ====================
    
    @Named("stringToActiveInactive")
    static ActiveInactive stringToActiveInactive(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return ActiveInactive.valueOf(value);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    @Named("activeInactiveToString")
    static String activeInactiveToString(ActiveInactive value) {
        return value != null ? value.name() : null;
    }
    
    // ... 其他枚举转换方法
}
```

### 2. 修改 Service 方法

#### buildProjectInfoDto 方法

```java
private ProjectInfoDto buildProjectInfoDto(ProjectInfo projectInfo) {
    try {
        // 1. 转换基本信息为DTO
        ProjectInfoDto projectInfoDto = ProjectConvert.INSTANCE.toDto(projectInfo);

        // 2. 查询项目要素
        ProjectElements elements = queryProjectElements(projectInfo.getProjectCode());
        if (ObjectUtil.isNotEmpty(elements)) {
            ProjectElementsDto elementsDto = ProjectConvert.INSTANCE.toDto(elements);
            projectInfoDto.setElements(elementsDto);
        } else {
            projectInfoDto.setElements(null);
        }

        // 3. 查询项目要素扩展
        ProjectElementsExt elementsExt = null;
        if (elements != null) {
            elementsExt = projectElementsExtMapper.selectByParentId(elements.getId());
        }
        if (elementsExt != null) {
            ProjectElementsExtDto elementsExtDto = ProjectConvert.INSTANCE.toDto(elementsExt);
            projectInfoDto.setElementsExt(elementsExtDto);
        } else {
            projectInfoDto.setElementsExt(null);
        }

        return projectInfoDto;
    } catch (Exception e) {
        logger.info("构建项目完整信息异常，projectCode: {}", projectInfo.getProjectCode(), e);
        return null;
    }
}
```

#### create 方法

```java
@Override
@Transactional(rollbackFor = Exception.class)
public void create(ProjectInfoDto resources) {
    this.checkProjectCode(resources.getProjectCode());
    ProjectInfo info = ProjectConvert.INSTANCE.toEntity(resources);
    
    String id = "P" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
    info.setId(id);
    String userId = String.valueOf(SecurityUtils.getCurrentUserId());
    LocalDateTime dateTime = LocalDateTime.now();
    info.setCreatedBy(userId);
    info.setCreatedTime(dateTime);
    projectInfoMapper.insert(info);

    if(ObjectUtil.isNotEmpty(resources.getElements())){
        ProjectElements elements = ProjectConvert.INSTANCE.toEntity(resources.getElements());
        
        String elemId = "ELEM" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
        elements.setId(elemId);
        elements.setProjectCode(info.getProjectCode());
        elements.setCreatedBy(userId);
        elements.setCreatedTime(dateTime);
        projectElementsMapper.insert(elements);

        if(ObjectUtil.isNotEmpty(resources.getElementsExt())){
            ProjectElementsExt elementsExt = ProjectConvert.INSTANCE.toEntity(resources.getElementsExt());
            
            String extId = "EXT" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
            elementsExt.setId(extId);
            elementsExt.setParentId(elemId);
            elementsExt.setProjectCode(info.getProjectCode());
            elementsExt.setCreatedBy(userId);
            elementsExt.setCreatedTime(dateTime);
            projectElementsExtMapper.insert(elementsExt);
        }
    }
}
```

#### update 方法

```java
@Override
@Transactional(rollbackFor = Exception.class)
public void update(ProjectInfoDto resources) {
    String userId = String.valueOf(SecurityUtils.getCurrentUserId());
    LocalDateTime dateTime = LocalDateTime.now();
    ProjectInfo projectInfo = projectInfoMapper.selectById(resources.getId());
    
    // 使用 Convert 进行转换，但保留原有的 ID 和审计字段
    ProjectInfo updatedInfo = ProjectConvert.INSTANCE.toEntity(resources);
    updatedInfo.setId(projectInfo.getId());
    updatedInfo.setCreatedBy(projectInfo.getCreatedBy());
    updatedInfo.setCreatedTime(projectInfo.getCreatedTime());
    updatedInfo.setRevision(projectInfo.getRevision());
    updatedInfo.setUpdatedBy(userId);
    updatedInfo.setUpdatedTime(dateTime);
    projectInfoMapper.updateById(updatedInfo);

    // ... 类似处理 elements 和 elementsExt
}
```

#### getProjectInfo 方法

```java
@Override
public ProjectInfoVo getProjectInfo(String id) {
    ProjectInfo info = projectInfoMapper.selectById(id);
    ProjectInfoVo vo = ProjectConvert.INSTANCE.toVo(info);
    
    ProjectElements projectElements = getProjectElements(info.getProjectCode());
    ProjectElementsVo elementsVo = ProjectConvert.INSTANCE.toVo(projectElements);
    vo.setElements(elementsVo);
    
    ProjectElementsExt projectElementExt = getProjectElementExt(projectElements.getId());
    ProjectElementsExtVo elementsExtVo = ProjectConvert.INSTANCE.toVo(projectElementExt);
    vo.setElementsExt(elementsExtVo);
    
    return vo;
}
```

## 关键优势

1. **符合系统规范**: 使用 MapStruct，与系统其他转换器保持一致
2. **类型安全**: 编译时检查，避免运行时错误
3. **性能优秀**: MapStruct 生成的代码性能优于反射
4. **易于维护**: 集中管理所有转换逻辑
5. **自动处理**: 自动处理枚举转换，包含异常处理

## 涉及的枚举字段

### ProjectElementsExt 相关枚举
- `allowCrossDayRepay` (ActiveInactive)
- `riskModelChannel` (RiskModelChannel)
- `loanPaymentChannel` (LoanPayChannel)
- `deductionBindCardChannel` (DeductChannel)
- `signChannel` (SignChannel)
- `smsChannel` (SmsChannel)
- `gracePeriodType` (GracePeriodType)

### ProjectElements 相关枚举
- `supportedRepayTypes` (RepaymentType)
- `capitalRoute` (CapitalRoute)
- `projectDurationType` (ProjectDurationType)
- `enabled` (AbleStatus)
- `graceNext` (ActiveInactive)

### ProjectInfo 相关枚举
- `enabled` (AbleStatus)

## 测试建议

1. 测试正常的枚举值转换
2. 测试无效枚举值的处理（返回 null）
3. 测试空值的处理
4. 测试 create、update、query 操作的完整流程
5. 验证 MapStruct 生成的代码是否正确

## 总结

通过使用 MapStruct 转换器，我们：
- 解决了枚举字段拷贝问题
- 符合了系统的技术规范
- 提供了更好的性能和类型安全
- 简化了代码维护
